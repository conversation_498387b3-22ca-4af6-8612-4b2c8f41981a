import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '智能任务管理',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.indigo),
        useMaterial3: true,
      ),
      home: const TaskManagerHomePage(),
    );
  }
}

// 任务数据模型
class Task {
  String id;
  String title;
  String description;
  bool isCompleted;
  String category;
  String priority; // 新增：优先级
  DateTime? dueDate; // 新增：截止日期
  DateTime createdAt;

  Task({
    required this.id,
    required this.title,
    this.description = '',
    this.isCompleted = false,
    this.category = '默认',
    this.priority = '普通', // 新增
    this.dueDate, // 新增
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'isCompleted': isCompleted,
      'category': category,
      'priority': priority,
      'dueDate': dueDate?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory Task.fromJson(Map<String, dynamic> json) {
    return Task(
      id: json['id'],
      title: json['title'],
      description: json['description'] ?? '',
      isCompleted: json['isCompleted'] ?? false,
      category: json['category'] ?? '默认',
      priority: json['priority'] ?? '普通',
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  // 检查是否过期
  bool get isOverdue {
    if (dueDate == null || isCompleted) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  // 获取优先级颜色
  Color get priorityColor {
    switch (priority) {
      case '高':
        return Colors.red;
      case '中':
        return Colors.orange;
      case '低':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}

// 主页面
class TaskManagerHomePage extends StatefulWidget {
  const TaskManagerHomePage({super.key});

  @override
  State<TaskManagerHomePage> createState() => _TaskManagerHomePageState();
}

class _TaskManagerHomePageState extends State<TaskManagerHomePage> {
  List<Task> tasks = [];
  String selectedCategory = '全部';
  String selectedPriority = '全部';
  String searchQuery = '';
  List<String> categories = ['全部', '工作', '学习', '生活', '其他'];
  List<String> priorities = ['全部', '高', '中', '低', '普通'];

  @override
  void initState() {
    super.initState();
    _loadTasks();
  }

  // 从本地存储加载任务
  Future<void> _loadTasks() async {
    final prefs = await SharedPreferences.getInstance();
    final tasksJson = prefs.getStringList('tasks') ?? [];
    setState(() {
      tasks = tasksJson
          .map((taskStr) => Task.fromJson(json.decode(taskStr)))
          .toList();
    });
  }

  // 保存任务到本地存储
  Future<void> _saveTasks() async {
    final prefs = await SharedPreferences.getInstance();
    final tasksJson = tasks.map((task) => json.encode(task.toJson())).toList();
    await prefs.setStringList('tasks', tasksJson);
  }

  // 添加新任务
  void _addTask(String title, String description, String category, String priority, DateTime? dueDate) {
    final newTask = Task(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      category: category,
      priority: priority,
      dueDate: dueDate,
      createdAt: DateTime.now(),
    );
    setState(() {
      tasks.add(newTask);
    });
    _saveTasks();
  }

  // 编辑任务
  void _editTask(Task task, String title, String description, String category, String priority, DateTime? dueDate) {
    setState(() {
      task.title = title;
      task.description = description;
      task.category = category;
      task.priority = priority;
      task.dueDate = dueDate;
    });
    _saveTasks();
  }

  // 切换任务完成状态
  void _toggleTaskCompletion(String taskId) {
    setState(() {
      final taskIndex = tasks.indexWhere((task) => task.id == taskId);
      if (taskIndex != -1) {
        tasks[taskIndex].isCompleted = !tasks[taskIndex].isCompleted;
      }
    });
    _saveTasks();
  }

  // 删除任务
  void _deleteTask(String taskId) {
    setState(() {
      tasks.removeWhere((task) => task.id == taskId);
    });
    _saveTasks();
  }

  // 获取过滤后的任务列表
  List<Task> get filteredTasks {
    List<Task> filtered = tasks;

    // 按分类过滤
    if (selectedCategory != '全部') {
      filtered = filtered.where((task) => task.category == selectedCategory).toList();
    }

    // 按优先级过滤
    if (selectedPriority != '全部') {
      filtered = filtered.where((task) => task.priority == selectedPriority).toList();
    }

    // 按搜索查询过滤
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((task) =>
          task.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
          task.description.toLowerCase().contains(searchQuery.toLowerCase())).toList();
    }

    // 按优先级和创建时间排序
    filtered.sort((a, b) {
      // 首先按完成状态排序（未完成的在前）
      if (a.isCompleted != b.isCompleted) {
        return a.isCompleted ? 1 : -1;
      }
      
      // 然后按优先级排序
      int priorityOrder = _getPriorityOrder(a.priority) - _getPriorityOrder(b.priority);
      if (priorityOrder != 0) return priorityOrder;
      
      // 最后按创建时间排序（最新的在前）
      return b.createdAt.compareTo(a.createdAt);
    });

    return filtered;
  }

  // 获取优先级排序值
  int _getPriorityOrder(String priority) {
    switch (priority) {
      case '高':
        return 0;
      case '中':
        return 1;
      case '低':
        return 2;
      case '普通':
        return 3;
      default:
        return 4;
    }
  }

  // 获取统计信息
  Map<String, int> get statistics {
    final total = tasks.length;
    final completed = tasks.where((task) => task.isCompleted).length;
    final overdue = tasks.where((task) => task.isOverdue).length;
    final today = tasks.where((task) {
        if (task.dueDate == null) return false;
        final now = DateTime.now();
        return task.dueDate!.year == now.year &&
               task.dueDate!.month == now.month &&
               task.dueDate!.day == now.day;
    }).length;

    return {
      'total': total,
      'completed': completed,
      'overdue': overdue,
      'today': today,
    };
  }

  @override
  Widget build(BuildContext context) {
    final stats = statistics;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('智能任务管理'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        elevation: 2,
        actions: [
          // 搜索按钮
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          // 分类筛选
          PopupMenuButton<String>(
            onSelected: (String category) {
              setState(() {
                selectedCategory = category;
              });
            },
            itemBuilder: (BuildContext context) {
              return categories.map((String category) {
                return PopupMenuItem<String>(
                  value: category,
                  child: Row(
                    children: [
                      Text(category),
                      if (category == selectedCategory)
                        const Icon(Icons.check, size: 16),
                    ],
                  ),
                );
              }).toList();
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(selectedCategory),
                  const Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // 统计信息卡片
          if (stats['total']! > 0)
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primaryContainer,
                    Theme.of(context).colorScheme.secondaryContainer,
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('总计', stats['total']!, Icons.list),
                  _buildStatItem('已完成', stats['completed']!, Icons.check_circle, Colors.green),
                  _buildStatItem('已过期', stats['overdue']!, Icons.warning, Colors.red),
                  _buildStatItem('今日', stats['today']!, Icons.today, Colors.blue),
                ],
              ),
            ),
          
          // 优先级筛选
          if (stats['total']! > 0)
            Container(
              height: 50,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: priorities.length,
                itemBuilder: (context, index) {
                  final priority = priorities[index];
                  final isSelected = selectedPriority == priority;
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(priority),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          selectedPriority = priority;
                        });
                      },
                      backgroundColor: isSelected ? Theme.of(context).colorScheme.primaryContainer : null,
                    ),
                  );
                },
              ),
            ),
          
          // 任务列表
          Expanded(
            child: filteredTasks.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.task_alt, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          '暂无任务',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                        Text('点击右下角按钮添加新任务', style: TextStyle(color: Colors.grey)),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: filteredTasks.length,
                    itemBuilder: (context, index) {
                      final task = filteredTasks[index];
                      return _buildTaskCard(task);
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddTaskDialog,
        icon: const Icon(Icons.add),
        label: const Text('添加任务'),
      ),
    );
  }

  // 构建统计项
  Widget _buildStatItem(String label, int value, IconData icon, [Color? color]) {
    return Column(
      children: [
        Icon(icon, color: color ?? Colors.grey),
        const SizedBox(height: 4),
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  // 构建任务卡片
  Widget _buildTaskCard(Task task) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: 2,
      child: ListTile(
        leading: Checkbox(
          value: task.isCompleted,
          onChanged: (_) => _toggleTaskCompletion(task.id),
        ),
        title: Text(
          task.title,
          style: TextStyle(
            decoration: task.isCompleted
                ? TextDecoration.lineThrough
                : TextDecoration.none,
            color: task.isCompleted ? Colors.grey : null,
            fontWeight: task.isOverdue ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (task.description.isNotEmpty)
              Text(
                task.description,
                style: TextStyle(
                  color: task.isCompleted ? Colors.grey : null,
                ),
              ),
            const SizedBox(height: 8),
            Row(
              children: [
                // 优先级标签
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: task.priorityColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    task.priority,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
                const SizedBox(width: 8),
                // 分类标签
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getCategoryColor(task.category),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    task.category,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
                const Spacer(),
                // 截止日期
                if (task.dueDate != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: task.isOverdue ? Colors.red : Colors.blue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _formatDate(task.dueDate!),
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditTaskDialog(task);
                break;
              case 'delete':
                _showDeleteConfirmDialog(task);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('删除', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 获取分类颜色
  Color _getCategoryColor(String category) {
    switch (category) {
      case '工作':
        return Colors.blue;
      case '学习':
        return Colors.green;
      case '生活':
        return Colors.orange;
      case '其他':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  // 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final taskDate = DateTime(date.year, date.month, date.day);
    
    if (taskDate == today) {
      return '今天';
    } else if (taskDate == today.add(const Duration(days: 1))) {
      return '明天';
    } else {
      return '${date.month}/${date.day}';
    }
  }

  // 显示搜索对话框
  void _showSearchDialog() {
    final searchController = TextEditingController(text: searchQuery);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('搜索任务'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              labelText: '搜索任务标题或描述',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.search),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () {
                setState(() {
                  searchQuery = '';
                });
                Navigator.of(context).pop();
              },
              child: const Text('清除'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  searchQuery = searchController.text.trim();
                });
                Navigator.of(context).pop();
              },
              child: const Text('搜索'),
            ),
          ],
        );
      },
    );
  }

  // 显示添加任务对话框
  void _showAddTaskDialog() {
    _showTaskDialog(null);
  }

  // 显示编辑任务对话框
  void _showEditTaskDialog(Task task) {
    _showTaskDialog(task);
  }

  // 显示任务对话框（添加或编辑）
  void _showTaskDialog(Task? task) {
    final titleController = TextEditingController(text: task?.title ?? '');
    final descriptionController = TextEditingController(text: task?.description ?? '');
    String selectedTaskCategory = task?.category ?? '工作';
    String selectedTaskPriority = task?.priority ?? '普通';
    DateTime? selectedDueDate = task?.dueDate;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(task == null ? '添加新任务' : '编辑任务'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: titleController,
                      decoration: const InputDecoration(
                        labelText: '任务标题',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: descriptionController,
                      decoration: const InputDecoration(
                        labelText: '任务描述（可选）',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: selectedTaskCategory,
                            decoration: const InputDecoration(
                              labelText: '分类',
                              border: OutlineInputBorder(),
                            ),
                            items: categories.skip(1).map((String category) {
                              return DropdownMenuItem<String>(
                                value: category,
                                child: Text(category),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              setDialogState(() {
                                selectedTaskCategory = newValue!;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: selectedTaskPriority,
                            decoration: const InputDecoration(
                              labelText: '优先级',
                              border: OutlineInputBorder(),
                            ),
                            items: priorities.skip(1).map((String priority) {
                              return DropdownMenuItem<String>(
                                value: priority,
                                child: Text(priority),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              setDialogState(() {
                                selectedTaskPriority = newValue!;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: const Text('截止日期'),
                      subtitle: Text(selectedDueDate != null
                          ? '${selectedDueDate!.year}-${selectedDueDate!.month.toString().padLeft(2, '0')}-${selectedDueDate!.day.toString().padLeft(2, '0')}'
                          : '无'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (selectedDueDate != null)
                            IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setDialogState(() {
                                  selectedDueDate = null;
                                });
                              },
                            ),
                          IconButton(
                            icon: const Icon(Icons.calendar_today),
                            onPressed: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: selectedDueDate ?? DateTime.now(),
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now().add(const Duration(days: 365)),
                              );
                              if (date != null) {
                                setDialogState(() {
                                  selectedDueDate = date;
                                });
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (titleController.text.trim().isNotEmpty) {
                      if (task == null) {
                        _addTask(
                          titleController.text.trim(),
                          descriptionController.text.trim(),
                          selectedTaskCategory,
                          selectedTaskPriority,
                          selectedDueDate,
                        );
                      } else {
                        _editTask(
                          task,
                          titleController.text.trim(),
                          descriptionController.text.trim(),
                          selectedTaskCategory,
                          selectedTaskPriority,
                          selectedDueDate,
                        );
                      }
                      Navigator.of(context).pop();
                    }
                  },
                  child: Text(task == null ? '添加' : '保存'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog(Task task) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除任务"${task.title}"吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                _deleteTask(task.id);
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }
}
